// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Urdu (`ur`).
class SUr extends S {
  SUr([String locale = 'ur']) : super(locale);

  @override
  String get appName => 'بجٹ منیجر پاکستان';

  @override
  String get dashboard => 'ڈیش بورڈ';

  @override
  String get transactions => 'لین دین';

  @override
  String get budgets => 'بجٹ';

  @override
  String get goals => 'اہداف';

  @override
  String get settings => 'ترتیبات';

  @override
  String get totalBalance => 'کل بیلنس';

  @override
  String get quickActions => 'فوری اعمال';

  @override
  String get addIncome => 'آمدنی شامل کریں';

  @override
  String get addExpense => 'خرچ شامل کریں';

  @override
  String get setBudget => 'بجٹ مقرر کریں';

  @override
  String get addGoal => 'ہدف شامل کریں';

  @override
  String get recentTransactions => 'حالیہ لین دین';

  @override
  String get spendingOverview => 'خرچ کا جائزہ';

  @override
  String get budgetOverview => 'بجٹ کا جائزہ';

  @override
  String get viewAll => 'سب دیکھیں';

  @override
  String get noTransactionsYet => 'ابھی تک کوئی لین دین نہیں';

  @override
  String get startByAddingTransaction =>
      'اپنا پہلا لین دین شامل کر کے شروع کریں';

  @override
  String get noBudgetsSet => 'کوئی بجٹ مقرر نہیں';

  @override
  String get createFirstBudget =>
      'خرچ کو ٹریک کرنے کے لیے اپنا پہلا بجٹ بنائیں';

  @override
  String get income => 'آمدنی';

  @override
  String get expense => 'خرچ';

  @override
  String get transfer => 'منتقلی';

  @override
  String get amount => 'رقم';

  @override
  String get description => 'تفصیل';

  @override
  String get category => 'قسم';

  @override
  String get account => 'اکاؤنٹ';

  @override
  String get date => 'تاریخ';

  @override
  String get notes => 'نوٹس';

  @override
  String get save => 'محفوظ کریں';

  @override
  String get cancel => 'منسوخ';

  @override
  String get delete => 'حذف کریں';

  @override
  String get edit => 'ترمیم';

  @override
  String get cash => 'نقد';

  @override
  String get bank => 'بینک';

  @override
  String get mobile => 'موبائل';

  @override
  String get creditCard => 'کریڈٹ کارڈ';

  @override
  String get today => 'آج';

  @override
  String get yesterday => 'کل';

  @override
  String get thisWeek => 'اس ہفتے';

  @override
  String get thisMonth => 'اس مہینے';

  @override
  String get thisYear => 'اس سال';

  @override
  String get weekly => 'ہفتہ وار';

  @override
  String get monthly => 'ماہانہ';

  @override
  String get quarterly => 'سہ ماہی';

  @override
  String get yearly => 'سالانہ';

  @override
  String get active => 'فعال';

  @override
  String get completed => 'مکمل';

  @override
  String get paused => 'رک گیا';

  @override
  String get cancelled => 'منسوخ';

  @override
  String get exceeded => 'حد سے زیادہ';

  @override
  String get nearLimit => 'حد کے قریب';

  @override
  String get onTrack => 'ٹھیک ہے';

  @override
  String get darkMode => 'ڈارک موڈ';

  @override
  String get language => 'زبان';

  @override
  String get currency => 'کرنسی';

  @override
  String get notifications => 'اطلاعات';

  @override
  String get backup => 'بیک اپ';

  @override
  String get export => 'برآمد';

  @override
  String get import => 'درآمد';

  @override
  String get about => 'کے بارے میں';

  @override
  String get version => 'ورژن';

  @override
  String get goodMorning => 'صبح بخیر';

  @override
  String get goodAfternoon => 'دوپہر بخیر';

  @override
  String get goodEvening => 'شام بخیر';

  @override
  String get financialTip => 'مالی مشورہ';

  @override
  String get profile => 'پروفائل';

  @override
  String get personalInformation => 'ذاتی معلومات';

  @override
  String get updateProfileDetails => 'اپنی پروفائل کی تفصیلات اپ ڈیٹ کریں';

  @override
  String get securitySettings => 'سیکیورٹی کی ترتیبات';

  @override
  String get changePasswordSecurity =>
      'پاس ورڈ اور سیکیورٹی کے اختیارات تبدیل کریں';

  @override
  String get notificationPreferences => 'اطلاع کی ترجیحات';

  @override
  String get manageNotificationSettings =>
      'اپنی اطلاع کی ترتیبات کا انتظام کریں';

  @override
  String get currencyLanguage => 'کرنسی اور زبان';

  @override
  String get setPreferredCurrencyLanguage =>
      'اپنی پسندیدہ کرنسی اور زبان سیٹ کریں';

  @override
  String get dataPrivacy => 'ڈیٹا اور پرائیویسی';

  @override
  String get exportData => 'ڈیٹا برآمد کریں';

  @override
  String get downloadFinancialData => 'اپنا مالی ڈیٹا ڈاؤن لوڈ کریں';

  @override
  String get backupSync => 'بیک اپ اور سنک';

  @override
  String get manageDataBackupSync =>
      'ڈیٹا بیک اپ اور سنکرونائزیشن کا انتظام کریں';

  @override
  String get privacyPolicy => 'پرائیویسی پالیسی';

  @override
  String get readPrivacyPolicy => 'ہماری پرائیویسی پالیسی پڑھیں';

  @override
  String get signOut => 'سائن آؤٹ';

  @override
  String get signOutConfirmation => 'کیا آپ واقعی سائن آؤٹ کرنا چاہتے ہیں؟';

  @override
  String get financialSummary => 'مالی خلاصہ';

  @override
  String get accountManagement => 'اکاؤنٹ کا انتظام';

  @override
  String get activeBudgets => 'فعال بجٹس';

  @override
  String get monthlyIncome => 'ماہانہ آمدنی';

  @override
  String get monthlyExpenses => 'ماہانہ اخراجات';

  @override
  String get spent => 'خرچ شدہ';

  @override
  String get budget => 'بجٹ';

  @override
  String get remaining => 'باقی';

  @override
  String get overBudgetBy => 'بجٹ سے زیادہ';

  @override
  String get normal => 'عام';

  @override
  String get live => 'براہ راست';

  @override
  String get recent => 'حالیہ';

  @override
  String get stale => 'پرانا';

  @override
  String get old => 'بہت پرانا';

  @override
  String get unknown => 'نامعلوم';

  @override
  String get dataFreshness => 'ڈیٹا کی تازگی';

  @override
  String get lastUpdated => 'آخری بار اپ ڈیٹ';

  @override
  String get noBudgetsYet => 'ابھی تک کوئی بجٹ نہیں';
}
